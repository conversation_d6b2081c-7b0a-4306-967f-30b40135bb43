<template>
  <div style="width: 100%">
    <Cascader
      :value="cpval"
      :fieldNames="{
        label: 'name',
        value: 'id',
        children: 'children',
      }"
      :options="options"
      :loadData="loadData"
      :popupClassName="'vben-ant-cascader-menus'"
      style="width: 100%"
      @change="methods.onChange"
      :placeholder
    />
  </div>
</template>

<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue';
  import { ref, computed, onMounted, watch } from 'vue';
  import type { CascaderProps } from 'ant-design-vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { size, toNumber, isFunction } from 'lodash-es';
  import { apiGetGlobalLocaltion } from '@/api/op/global';
  import type { GlobalLocaltion } from '@/api/op/model/global';

  defineOptions({ name: 'RegionSelect' });
  type ValueType = number | string;
  type ValueState = ValueType[];
  interface Props {
    value?: ValueState;
    level?: number | string;
    separator?: string;
    placeholder?: string;
    afterFetch?: (...arg: any) => Promise<Recordable<any>>;
  }

  const props = withDefaults(defineProps<Props>(), {
    level: 3,
    value: () => [],
    separator: '/',
  });

  const options = ref<any[]>([]);
  const loading = ref<boolean>(true);

  defineEmits(['update:value', 'change']);

  const cpval = computed(() => {
    // 由于现在绑定的是 id 值，需要通过其他方式来获取显示值
    // 这里返回空数组，让级联选择器根据当前选中的 options 来显示
    // 实际的显示值会通过 Cascader 组件的内部逻辑来处理
    if (!props.value || !Array.isArray(props.value)) return [];

    // 过滤掉 undefined 和 null 值
    return props.value.filter((item) => item !== undefined && item !== null);
  });

  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', [
    'change',
    'update:value',
  ]);

  const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      targetOption.loading = true;
      let res = await methods.reqGlobalLocation(targetOption.id);
      if (size(res)) {
        targetOption.children = res;
      } else {
        targetOption.children = [];
        targetOption.isLeaf = true;
      }
      targetOption.loading = false;
    } catch (error) {
      targetOption.loading = false;
    }
  };

  const methods = {
    // 初始化 - 获取根级数据（国家级，parentId为0或null）
    async init() {
      try {
        loading.value = true;
        let res = await methods.reqGlobalLocation(undefined);
        options.value = res;
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    },
    // 使用新的全球位置接口获取数据
    async reqGlobalLocation(pid: number | undefined) {
      try {
        const res = await apiGetGlobalLocaltion(pid);
        // 处理返回的数据，可能是单个对象或数组
        const dataArray = Array.isArray(res) ? res : [res];
        let l = dataArray.map((item: GlobalLocaltion) => {
          return {
            // 包含所有 GlobalLocaltion 接口字段
            id: item.id,
            parentId: item.parentId,
            level: item.level,
            path: item.path,
            code: item.code,
            languageCode: item.languageCode,
            countryNumber: item.countryNumber,
            phoneNumber: item.phoneNumber,
            abbreviation: item.abbreviation,
            iso: item.iso,
            chineseName: item.chineseName,
            foreignName: item.foreignName,
            pathChinese: item.pathChinese,
            pathForeign: item.pathForeign,
            pinyin: item.pinyin,
            // 组件内部使用的字段
            name: item.chineseName || item.foreignName,
            isLeaf: methods.checkCondition(item.level),
          };
        });

        const { afterFetch } = props;

        if (afterFetch && isFunction(afterFetch)) {
          const result = await afterFetch(l);
          l = Array.isArray(result) ? result : l;
        }

        return Promise.resolve(l);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    displayRender({ labels }) {
      return labels.join(props.separator);
    },
    onChange(value: any, _selectedOptions: any) {
      // value 参数现在直接包含了每个层级的 id 值数组
      // selectedOptions 包含了完整的选项数据

      if (!value) {
        value = [];
      }

      // 直接使用 Cascader 传递的 value（id 数组）
      state.value = value;
    },

    checkCondition(level: number) {
      // level 是数字类型，直接比较
      // 如果当前层级大于等于设定的最大层级，则为叶子节点
      let isLeaf = level >= toNumber(props.level);
      return isLeaf;
    },
    // 拼接字符串拆分
    splitString(str: string, index: number, separator: string = ',') {
      if (str) {
        return str.split(separator)[index] || '';
      }
      return '';
    },
  };

  onMounted(() => {
    methods.init();
  });

  // 监听 level 的变化
  watch(
    () => props.level,
    (newLevel, oldLevel) => {
      if (newLevel !== oldLevel) {
        methods.init();
      }
    },
  );
</script>

<style lang="less">
  .vben-ant-cascader-menus {
    .ant-cascader-menu {
      height: 300px !important;
    }
  }
</style>
