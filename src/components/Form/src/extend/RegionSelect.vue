<template>
  <div style="width: 100%">
    <Cascader
      :value="cpval"
      :fieldNames="{
        label: 'name',
        value: 'id',
        children: 'children',
      }"
      :options="options"
      :loadData="loadData"
      :popupClassName="'vben-ant-cascader-menus'"
      style="width: 100%"
      @change="methods.onChange"
      :placeholder
      :showSearch="true"
    >
      <template #suffixIcon v-if="dataLoading">
        <LoadingOutlined spin />
      </template>
      <template #notFoundContent v-if="dataLoading">
        <span>
          <LoadingOutlined spin class="mr-1" />
          加载中...
        </span>
      </template>
    </Cascader>
  </div>
</template>

<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue';
  import { ref, computed, onMounted, watch } from 'vue';
  import type { CascaderProps } from 'ant-design-vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { size, toNumber, isFunction } from 'lodash-es';
  import { apiGetGlobalLocaltion } from '@/api/op/global';
  import type { GlobalLocaltion } from '@/api/op/model/global';
  import { LoadingOutlined } from '@ant-design/icons-vue';

  defineOptions({ name: 'RegionSelect' });
  type ValueType = number | string;
  type ValueState = ValueType[];
  interface Props {
    value?: ValueState;
    level?: number | string;
    separator?: string;
    placeholder?: string;
    afterFetch?: (...arg: any) => Promise<Recordable<any>>;
  }

  const props = withDefaults(defineProps<Props>(), {
    level: 3,
    value: () => [],
    separator: '/',
  });

  const options = ref<any[]>([]);
  const loading = ref<boolean>(true);
  const dataLoading = ref<boolean>(false);
  const dataReady = ref<boolean>(false);
  const isInitializing = ref<boolean>(true);

  defineEmits(['update:value', 'change']);

  const cpval = computed(() => {
    // 只在初始化阶段不显示值，避免下拉选择时的闪烁
    if (isInitializing.value && (dataLoading.value || loading.value || !dataReady.value)) return [];

    if (!props.value || !Array.isArray(props.value)) return [];

    // 过滤掉 undefined 和 null 值
    return props.value.filter((item) => item !== undefined && item !== null);
  });

  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', [
    'change',
    'update:value',
  ]);

  const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      targetOption.loading = true;
      // 注意：这里不设置 dataLoading.value = true，避免影响当前显示的值
      let res = await methods.reqGlobalLocation(targetOption.id);
      if (size(res)) {
        targetOption.children = res;
      } else {
        targetOption.children = [];
        targetOption.isLeaf = true;
      }
      targetOption.loading = false;
    } catch (error) {
      targetOption.loading = false;
    }
    // 注意：这里也不需要设置 dataLoading.value = false
  };

  const methods = {
    // 初始化 - 获取根级数据（国家级，parentId为0或null）
    async init() {
      try {
        loading.value = true;
        dataLoading.value = true;
        dataReady.value = false;
        let res = await methods.reqGlobalLocation(undefined);
        options.value = res;
        // 如果没有初始值，初始化完成后就可以标记为准备好
        if (!props.value || props.value.length === 0) {
          dataReady.value = true;
          isInitializing.value = false;
        }
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
        dataLoading.value = false;
      }
    },
    // 使用新的全球位置接口获取数据
    async reqGlobalLocation(pid: number | undefined) {
      try {
        const res = await apiGetGlobalLocaltion(pid);
        // 处理返回的数据，可能是单个对象或数组
        const dataArray = Array.isArray(res) ? res : [res];
        let l = dataArray.map((item: GlobalLocaltion) => {
          return {
            // 包含所有 GlobalLocaltion 接口字段
            id: item.id,
            parentId: item.parentId,
            level: item.level,
            path: item.path,
            code: item.code,
            languageCode: item.languageCode,
            countryNumber: item.countryNumber,
            phoneNumber: item.phoneNumber,
            abbreviation: item.abbreviation,
            iso: item.iso,
            chineseName: item.chineseName,
            foreignName: item.foreignName,
            pathChinese: item.pathChinese,
            pathForeign: item.pathForeign,
            pinyin: item.pinyin,
            // 组件内部使用的字段
            name: item.chineseName || item.foreignName,
            isLeaf: methods.checkCondition(item.level),
          };
        });

        const { afterFetch } = props;

        if (afterFetch && isFunction(afterFetch)) {
          const result = await afterFetch(l);
          l = Array.isArray(result) ? result : l;
        }

        return Promise.resolve(l);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    displayRender({ labels }) {
      return labels.join(props.separator);
    },
    onChange(value: any, _selectedOptions: any) {
      // value 参数现在直接包含了每个层级的 id 值数组
      // selectedOptions 包含了完整的选项数据

      if (!value) {
        value = [];
      }

      // 直接使用 Cascader 传递的 value（id 数组）
      state.value = value;
    },

    checkCondition(level: number) {
      // level 是数字类型，直接比较
      // 如果当前层级大于等于设定的最大层级，则为叶子节点
      let isLeaf = level >= toNumber(props.level);
      return isLeaf;
    },
    // 拼接字符串拆分
    splitString(str: string, index: number, separator: string = ',') {
      if (str) {
        return str.split(separator)[index] || '';
      }
      return '';
    },

    // 根据初始值加载对应的数据层级
    async loadInitialData(valueIds: ValueType[]) {
      if (!valueIds || valueIds.length === 0) {
        dataReady.value = true;
        return;
      }

      try {
        dataLoading.value = true;
        dataReady.value = false;

        // 确保根级数据已加载
        if (options.value.length === 0) {
          await methods.init();
        }

        // 逐级加载数据
        let currentOptions = options.value;

        for (let i = 0; i < valueIds.length; i++) {
          const currentId = valueIds[i];
          const numericId = typeof currentId === 'string' ? parseInt(currentId, 10) : currentId;

          // 在当前级别中查找对应的选项
          const currentOption = currentOptions.find((option) => option.id === numericId);

          if (currentOption) {
            // 如果不是最后一级且没有子数据，则加载子数据
            if (i < valueIds.length - 1 && !currentOption.children) {
              const childrenData = await methods.reqGlobalLocation(numericId);
              currentOption.children = childrenData;
            }

            // 移动到下一级
            if (currentOption.children) {
              currentOptions = currentOption.children;
            }
          }
        }

        // 所有数据加载完成后，标记数据已准备好
        dataReady.value = true;
      } catch (error) {
        console.error('加载初始数据失败:', error);
      } finally {
        dataLoading.value = false;
      }
    },
  };

  onMounted(() => {
    methods.init();
  });

  // 监听 value 的变化，用于数据回显
  watch(
    () => props.value,
    async (newValue) => {
      if (newValue && Array.isArray(newValue) && newValue.length > 0) {
        // 等待初始化完成后再加载初始数据
        if (loading.value) {
          await new Promise((resolve) => {
            const unwatch = watch(loading, (isLoading) => {
              if (!isLoading) {
                unwatch();
                resolve(void 0);
              }
            });
          });
        }
        await methods.loadInitialData(newValue);
      } else {
        // 如果没有初始值，确保加载状态被重置，数据标记为准备好
        dataLoading.value = false;
        dataReady.value = true;
      }
    },
    { immediate: true },
  );

  // 监听 level 的变化
  watch(
    () => props.level,
    (newLevel, oldLevel) => {
      if (newLevel !== oldLevel) {
        methods.init();
      }
    },
  );
</script>

<style lang="less">
  .vben-ant-cascader-menus {
    .ant-cascader-menu {
      height: 300px !important;
    }
  }
</style>
